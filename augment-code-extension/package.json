{"name": "augment-code-extension", "displayName": "Augment Code Extension", "description": "An extension that augments code with intelligent suggestions.", "version": "0.1.0", "publisher": "your-name", "engines": {"vscode": "^1.50.0"}, "activationEvents": ["onCommand:augmentCode.augmentText", "onCommand:augmentCode.getAugmentedSuggestions"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "augmentCode.augmentText", "title": "Augment Text"}, {"command": "augmentCode.getAugmentedSuggestions", "title": "Get Augmented Suggestions"}]}, "scripts": {"vscode:prepublish": "webpack --mode production", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "postinstall": "node ./out/compile.js"}, "devDependencies": {"typescript": "^4.0.0", "webpack": "^5.0.0", "webpack-cli": "^4.0.0", "vscode": "^1.1.36"}, "dependencies": {}}
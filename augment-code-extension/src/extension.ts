import * as vscode from 'vscode';
import { AugmentCode } from './augment/augmentCode';

export function activate(context: vscode.ExtensionContext) {
    const augmentCode = new AugmentCode();

    let disposable = vscode.commands.registerCommand('augmentCode.augmentText', async () => {
        const editor = vscode.window.activeTextEditor;
        if (editor) {
            const document = editor.document;
            const selection = editor.selection;
            const text = document.getText(selection);

            const augmentedText = augmentCode.augmentText(text);
            editor.edit(editBuilder => {
                editBuilder.replace(selection, augmentedText);
            });
        }
    });

    context.subscriptions.push(disposable);
}

export function deactivate() {}